@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors - Earth-inspired palette */
  --primary-brown: #8B4513;
  --primary-gold: #DAA520;
  --primary-green: #556B2F;
  
  /* Secondary Colors - Wildlife tones */
  --secondary-sunset: #FF6B35;
  --secondary-sky: #4A90E2;
  --secondary-stone: #8D8B7A;
  --secondary-cream: #F5F5DC;
  
  /* Activity Differentiation */
  --hunting-accent: #B8860B;
  --photo-accent: #4169E1;
  
  /* Neutrals */
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --medium-gray: #6C757D;
  --dark-gray: #343A40;
  --black: #000000;
  
  /* Typography */
  --font-display: 'Segoe UI', system-ui, sans-serif;
  --font-body: 'Segoe UI', system-ui, sans-serif;
  --font-ui: 'Segoe UI', system-ui, sans-serif;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-xxl: 3rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 1px 3px rgba(0,0,0,0.06);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
  --shadow-xl: 0 20px 25px rgba(0,0,0,0.15), 0 10px 10px rgba(0,0,0,0.04);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-body);
  line-height: 1.6;
  color: var(--dark-gray);
  background: var(--light-gray);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: var(--space-md);
}

h1 { 
  font-size: 3rem; 
  color: var(--primary-brown); 
}

h2 { 
  font-size: 2.5rem; 
  color: var(--primary-brown); 
}

h3 { 
  font-size: 2rem; 
  color: var(--primary-green); 
}

h4 { 
  font-size: 1.5rem; 
}

h5 { 
  font-size: 1.25rem; 
}

h6 { 
  font-size: 1rem; 
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font-family: var(--font-ui);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.slide-up:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* Utility classes */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile responsive */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}
