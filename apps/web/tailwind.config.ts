import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        earth: {
          100: "#F5F5DC",
          200: "#F8F9FA", 
          300: "#8D8B7A",
          500: "#6C757D",
          600: "#556B2F",
          700: "#343A40",
          900: "#8B4513",
        },
        accent: {
          500: "#4A90E2",
          600: "#DAA520", 
          700: "#B8860B",
        },
        "hunting-accent": "#B8860B",
        "photo-accent": "#4169E1",
      },
      fontFamily: {
        sans: ["var(--font-inter)", "Segoe UI", "system-ui", "sans-serif"],
        serif: ["Georgia", "serif"],
        mono: ["var(--font-inter)", "-apple-system", "sans-serif"],
      },
    },
  },
  plugins: [],
};

export default config;